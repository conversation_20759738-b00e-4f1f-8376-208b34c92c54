# AppSolve 品牌迁移技术方案

## 项目概述

本方案旨在将当前的 ShipAny 模板（Web AI SaaS 建站工具）迁移为 AppSolve 落地页（iOS AI 应用开发模板）。

### 核心差异
- **ShipAny**: Next.js Web应用模板，用于构建 AI SaaS 网站
- **AppSolve**: iOS 应用开发模板，包含 SwiftUI + Firebase + AI 功能

## 技术架构分析

### 当前架构
- **前端框架**: Next.js 15.2.3 + TypeScript
- **UI 组件**: Radix UI + Tailwind CSS
- **国际化**: next-intl (支持中英文)
- **认证**: NextAuth
- **支付**: Stripe
- **数据库**: Drizzle ORM + PostgreSQL/Supabase
- **部署**: Vercel/Cloudflare

### 保留的架构
落地页仍使用现有的 Next.js 架构，但内容需要全面调整为 iOS 应用模板的展示。

## 迁移方案

### 第一阶段：品牌和内容迁移

#### 1. 品牌资源更新
- [ ] 更新 Logo 文件 (`/public/logo.png`)
- [ ] 更新 favicon 和其他品牌图标
- [ ] 准备 iOS 应用截图替换现有的 Web 截图

#### 2. 国际化内容更新

**主要文件**:
- `src/i18n/pages/landing/en.json`
- `src/i18n/pages/landing/zh.json`

**更新内容**:
- 项目名称: ShipAny → AppSolve
- 标语: "Ship Any AI Startups" → "你的下一个爆款 iOS 应用的完美起点"
- 描述: Web SaaS → iOS 应用模板
- 技术栈: Next.js/React → SwiftUI/Firebase/RevenueCat

#### 3. 页面内容调整

**Hero 区块**:
```json
{
  "title": "Launch Your Hit iOS App in Days, Not Months",
  "highlight_text": "iOS App",
  "description": "AI-powered iOS app template with SwiftUI, Firebase, and RevenueCat.<br/>Skip months of development with production-ready features."
}
```

**Features 区块**:
- 即用型身份验证 (Apple ID, Google, Email/OTP)
- 订阅支付系统 (RevenueCat)
- AI 设计生成引擎
- 完整媒体管理系统
- 视频效果和处理
- Firebase 后端集成

**技术栈展示**:
- SwiftUI → 替换 Next.js
- Firebase → 替换 Supabase
- RevenueCat → 替换 Stripe (Web)
- Combine → 替换 React
- Xcode → 替换 Vercel

### 第二阶段：功能页面调整

#### 1. 定价页面 (`/pricing`)
- 调整为 iOS 应用模板定价
- Solo Developer: $499 (个人版)
- Business Pro: $999 (商业版)
- 包含的服务内容更新

#### 2. 案例展示页面 (`/showcase`)
- 替换为 iOS 应用案例
- AIWallpaper.shop (iOS 版)
- HeyBeauty.ai (iOS 版)
- 其他 AI iOS 应用案例

#### 3. 文档链接
- 更新文档链接指向 iOS 开发文档
- 更新 GitHub 仓库链接
- 更新社区链接

### 第三阶段：图片和媒体资源

#### 需要准备的资源
1. **iOS 应用截图** (建议尺寸: 1290x2796 for iPhone)
   - 创建界面
   - 付费墙界面
   - 个人资料界面
   - AI 生成界面

2. **功能展示图**
   - SwiftUI 代码示例
   - Firebase 集成展示
   - RevenueCat 配置
   - AI 功能演示

3. **技术架构图**
   - iOS 应用架构图
   - 数据流程图

### 第四阶段：SEO 和元数据

#### 1. 环境变量更新
```env
NEXT_PUBLIC_PROJECT_NAME = "AppSolve"
NEXT_PUBLIC_WEB_URL = "https://appsolve.ai"
```

#### 2. 页面元数据
- 更新页面标题和描述
- 更新 OG 图片
- 更新关键词


## 实施步骤

### Phase 1: 准备工作 (1-2天)
1. 备份当前项目
2. 创建新分支 `appsolve-migration`
3. 准备所有媒体资源
4. 设计 iOS 应用截图

### Phase 2: 内容迁移 (2-3天)
1. 更新所有 i18n 文件
2. 更新组件中的硬编码文本
3. 替换所有品牌相关内容
4. 更新定价和服务内容

### Phase 3: 视觉更新 (1-2天)
1. 更新所有图片资源
2. 调整 UI 风格以匹配 iOS 设计语言
3. 更新配色方案（如需要）

## 技术注意事项

1. **保持 URL 结构**：为了 SEO，尽量保持现有的 URL 结构
2. **重定向设置**：设置必要的 301 重定向
3. **图片优化**：使用 WebP 格式，优化加载速度
4. **缓存策略**：合理设置静态资源缓存
5. **国际化**：确保中英文内容完整且准确

## 风险评估

1. **SEO 影响**：品牌切换可能短期影响搜索排名
2. **用户混淆**：需要明确说明产品定位变化
3. **技术兼容**：确保所有功能正常工作

## 后续维护

1. 定期更新 iOS 应用案例
2. 同步更新技术文档
3. 收集用户反馈并优化
4. 跟踪转化率并调整内容

## 总结

整个迁移过程预计需要 7-10 个工作日。关键是准备好所有的 iOS 应用相关资源，并确保内容准确传达 AppSolve 的价值主张：帮助开发者快速构建高质量的 iOS AI 应用。