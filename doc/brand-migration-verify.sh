#!/bin/bash

# Brand Migration Verification Script
# This script verifies that the ShipAny to AppSolve migration was completed successfully
# 
# Usage: ./brand-migration-verify.sh

set -e

echo "🔍 Brand Migration Verification"
echo "==============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Function to check if a file contains specific text
check_file_contains() {
    local file="$1"
    local search="$2"
    local description="$3"
    local should_contain="$4"  # true or false
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [[ ! -f "$file" ]]; then
        echo -e "${YELLOW}⚠️  WARNING: File $file not found${NC}"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
        return
    fi
    
    if grep -q "$search" "$file"; then
        if [[ "$should_contain" == "true" ]]; then
            echo -e "${GREEN}✅ PASS: $description${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${RED}❌ FAIL: $description (found '$search' in $file)${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    else
        if [[ "$should_contain" == "true" ]]; then
            echo -e "${RED}❌ FAIL: $description (not found '$search' in $file)${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            echo -e "${GREEN}✅ PASS: $description${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    fi
}

# Function to check directory for old brand references
check_directory_clean() {
    local dir="$1"
    local search="$2"
    local description="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [[ ! -d "$dir" ]]; then
        echo -e "${YELLOW}⚠️  WARNING: Directory $dir not found${NC}"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
        return
    fi
    
    local found_files=$(find "$dir" -type f \( -name "*.json" -o -name "*.md" -o -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.mdx" \) -exec grep -l "$search" {} \; 2>/dev/null || true)
    
    if [[ -z "$found_files" ]]; then
        echo -e "${GREEN}✅ PASS: $description${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}❌ FAIL: $description${NC}"
        echo -e "${RED}   Found in: $found_files${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

echo "🔧 Checking Core Configuration Files"
echo "===================================="

# Check package.json
check_file_contains "package.json" "appsolve-template-one" "package.json has correct project name" "true"
check_file_contains "package.json" "https://appsolve.ai" "package.json has correct homepage URL" "true"
check_file_contains "package.json" "shipany" "package.json contains no old brand references" "false"

# Check environment files
check_file_contains ".env.example" "AppSolve" "Environment file has correct project name" "true"
check_file_contains ".env.example" "ShipAny" "Environment file contains no old brand references" "false"

check_file_contains "wrangler.toml.example" "appsolve-template-one" "Wrangler config has correct project name" "true"
check_file_contains "wrangler.toml.example" "AppSolve" "Wrangler config has correct project name variable" "true"
check_file_contains "wrangler.toml.example" "shipany" "Wrangler config contains no old brand references" "false"

# Check README
check_file_contains "README.md" "AppSolve Template One" "README has correct title" "true"
check_file_contains "README.md" "Build Any AI SaaS Startups" "README has updated tagline" "true"
check_file_contains "README.md" "appsolveai/appsolve-template-one" "README has correct GitHub URLs" "true"
check_file_contains "README.md" "https://appsolve.ai" "README has correct website URLs" "true"
check_file_contains "README.md" "ShipAny" "README contains no old brand references" "false"

echo ""
echo "🌐 Checking Internationalization Content"
echo "========================================"

# Check English messages
check_file_contains "src/i18n/messages/en.json" "Build Any AI SaaS Startups in hours | AppSolve" "English metadata has correct title" "true"
check_file_contains "src/i18n/messages/en.json" "AppSolve, AI SaaS Boilerplate" "English metadata has correct keywords" "true"
check_file_contains "src/i18n/messages/en.json" "ShipAny" "English messages contain no old brand references" "false"

# Check Chinese messages
check_file_contains "src/i18n/messages/zh.json" "几小时内构建任何 AI SaaS 创业项目 | AppSolve" "Chinese metadata has correct title" "true"
check_file_contains "src/i18n/messages/zh.json" "AppSolve, AI SaaS 模板" "Chinese metadata has correct keywords" "true"
check_file_contains "src/i18n/messages/zh.json" "ShipAny" "Chinese messages contain no old brand references" "false"

# Check landing pages
check_directory_clean "src/i18n/pages/landing" "ShipAny" "Landing pages contain no old brand references"
check_directory_clean "src/i18n/pages/showcase" "ShipAny" "Showcase pages contain no old brand references"
check_directory_clean "src/i18n/pages/pricing" "ShipAny" "Pricing pages contain no old brand references"

echo ""
echo "📄 Checking Legal and Documentation"
echo "==================================="

# Check LICENSE
check_file_contains "LICENSE" "AppSolve AI SaaS Boilerplate License Agreement" "LICENSE has correct title" "true"
check_file_contains "LICENSE" "AppSolve (\"Licensor\")" "LICENSE has correct licensor name" "true"
check_file_contains "LICENSE" "ShipAny" "LICENSE contains no old brand references" "false"

# Check privacy policy
check_file_contains "src/app/(legal)/privacy-policy/page.mdx" "Privacy Policy for AppSolve" "Privacy policy has correct title" "true"
check_file_contains "src/app/(legal)/privacy-policy/page.mdx" "ShipAny" "Privacy policy contains no old brand references" "false"

echo ""
echo "🔍 Checking for Global References"
echo "================================="

# Check for any remaining shipany references (case insensitive)
echo "Checking for remaining 'shipany' references..."
REMAINING_SHIPANY=$(find . -type f \( -name "*.json" -o -name "*.md" -o -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.mdx" \) -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./backup-*/*" -not -path "./doc/brand-migration-*" -exec grep -l -i "shipany" {} \; 2>/dev/null || true)

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [[ -z "$REMAINING_SHIPANY" ]]; then
    echo -e "${GREEN}✅ PASS: No remaining 'shipany' references found${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAIL: Found remaining 'shipany' references in:${NC}"
    echo -e "${RED}$REMAINING_SHIPANY${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

# Check for shipany.ai domain references
echo "Checking for remaining 'shipany.ai' domain references..."
DOMAIN_REFS=$(find . -type f \( -name "*.json" -o -name "*.md" -o -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.mdx" \) -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./backup-*/*" -not -path "./doc/brand-migration-*" -exec grep -l "shipany\.ai" {} \; 2>/dev/null || true)

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [[ -z "$DOMAIN_REFS" ]]; then
    echo -e "${GREEN}✅ PASS: No remaining 'shipany.ai' domain references found${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ FAIL: Found remaining 'shipany.ai' domain references in:${NC}"
    echo -e "${RED}$DOMAIN_REFS${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

echo ""
echo "📁 Checking Static Assets"
echo "========================="

# Check if logo and favicon exist
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [[ -f "public/logo.png" ]]; then
    echo -e "${GREEN}✅ PASS: Logo file exists${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  WARNING: Logo file (public/logo.png) not found - please update with AppSolve logo${NC}"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [[ -f "public/favicon.ico" ]]; then
    echo -e "${GREEN}✅ PASS: Favicon file exists${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  WARNING: Favicon file (public/favicon.ico) not found - please update with AppSolve icon${NC}"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
fi

echo ""
echo "🧪 Checking Build Configuration"
echo "==============================="

# Check if the project can be built
echo "Testing project build..."
if command -v pnpm &> /dev/null; then
    if pnpm build > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS: Project builds successfully${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}❌ FAIL: Project build failed${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  WARNING: pnpm not found, skipping build test${NC}"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
fi

echo ""
echo "📊 Verification Summary"
echo "======================="
echo -e "Total Checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"

echo ""
if [[ $FAILED_CHECKS -eq 0 ]]; then
    echo -e "${GREEN}🎉 Migration Verification PASSED!${NC}"
    echo -e "${GREEN}All critical checks passed successfully.${NC}"
    
    if [[ $WARNING_CHECKS -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Please address the warnings above.${NC}"
    fi
    
    echo ""
    echo "📋 Recommended Next Steps:"
    echo "1. Update logo and favicon files if not already done"
    echo "2. Test the application locally: pnpm dev"
    echo "3. Test all major features and pages"
    echo "4. Update any external service configurations"
    echo "5. Deploy to staging environment for testing"
    echo "6. Update DNS and domain configurations if needed"
    
    exit 0
else
    echo -e "${RED}❌ Migration Verification FAILED!${NC}"
    echo -e "${RED}Please fix the failed checks above before proceeding.${NC}"
    exit 1
fi
