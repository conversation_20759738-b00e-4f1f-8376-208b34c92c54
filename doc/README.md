# ShipAny → AppSolve 品牌迁移文档

本目录包含了将 ShipAny 品牌名称全面迁移到 AppSolve 的完整技术方案和工具。

## 📁 文档结构

### 核心文档
- **`brand-migration-technical-plan.md`** - 详细的技术实施方案
- **`brand-migration-checklist.md`** - 完整的实施检查清单
- **`README.md`** - 本文档，使用指南

### 自动化工具
- **`brand-migration-script.sh`** - 自动化迁移脚本
- **`brand-migration-verify.sh`** - 迁移验证脚本

## 🚀 快速开始

### 方法一：使用自动化脚本（推荐）

1. **备份代码**（重要！）
   ```bash
   git add .
   git commit -m "Backup before brand migration"
   git checkout -b feature/brand-migration-appsolve
   ```

2. **运行迁移脚本**
   ```bash
   ./doc/brand-migration-script.sh
   ```

3. **验证迁移结果**
   ```bash
   ./doc/brand-migration-verify.sh
   ```

4. **手动更新静态资源**
   - 替换 `public/logo.png` 为 AppSolve Logo
   - 替换 `public/favicon.ico` 为 AppSolve 图标
   - 更新 `preview.png` 项目预览图

5. **测试和提交**
   ```bash
   pnpm dev          # 测试本地开发
   pnpm build        # 测试构建
   git add .
   git commit -m "feat: migrate brand from ShipAny to AppSolve"
   ```

### 方法二：手动执行

如果你更喜欢手动控制每个步骤，请按照以下顺序执行：

1. 阅读 `brand-migration-technical-plan.md` 了解完整方案
2. 按照 `brand-migration-checklist.md` 逐项执行
3. 使用 `brand-migration-verify.sh` 验证结果

## 📋 迁移范围

### 配置文件
- [x] package.json
- [x] .env.example
- [x] wrangler.toml.example
- [x] README.md
- [x] Dockerfile

### 国际化内容
- [x] 英文内容 (src/i18n/messages/en.json)
- [x] 中文内容 (src/i18n/messages/zh.json)
- [x] 首页内容 (src/i18n/pages/landing/)
- [x] 展示页面 (src/i18n/pages/showcase/)
- [x] 定价页面 (src/i18n/pages/pricing/)

### 法律文档
- [x] LICENSE 许可协议
- [x] 隐私政策 (src/app/(legal)/privacy-policy/)

### 营销文档
- [x] doc/mkt/ 目录下的营销材料

### 静态资源
- [ ] public/logo.png (需要手动替换)
- [ ] public/favicon.ico (需要手动替换)
- [ ] preview.png (需要手动更新)

## 🔍 品牌名称映射

| 原品牌名称 | 新品牌名称 | 说明 |
|-----------|-----------|------|
| ShipAny | AppSolve | 主品牌名称 |
| Ship Any | Build Any | 标语中的动词 |
| shipany | appsolve | 小写形式 |
| shipany-template-one | appsolve-template-one | 项目名称 |
| shipanyai | appsolveai | GitHub 组织名 |
| shipany.ai | appsolve.ai | 域名 |
| docs.shipany.ai | docs.appsolve.ai | 文档域名 |

## ⚠️ 注意事项

### 迁移前必读
1. **备份重要**：确保在开始迁移前有完整的代码备份
2. **分支管理**：建议在新分支上进行迁移操作
3. **测试充分**：每个阶段完成后都要进行功能测试

### 已知限制
1. **静态资源**：Logo 和图标需要手动替换
2. **外部服务**：可能需要更新第三方服务的配置
3. **域名配置**：如果使用自定义域名，需要单独配置

### 风险评估
- **低风险**：文本内容替换
- **中风险**：配置文件修改
- **高风险**：构建和部署配置

## 🧪 测试建议

### 本地测试
```bash
# 启动开发服务器
pnpm dev

# 测试构建
pnpm build

# 测试类型检查
pnpm lint
```

### 功能测试清单
- [ ] 首页显示正确的品牌名称
- [ ] 多语言切换正常工作
- [ ] 所有页面的品牌显示一致
- [ ] 外部链接指向正确
- [ ] 用户认证流程正常
- [ ] 支付流程正常（如果适用）

### 部署测试
- [ ] Vercel 部署成功
- [ ] Cloudflare Workers 部署成功（如果使用）
- [ ] 生产环境品牌显示正确

## 🔧 故障排除

### 常见问题

**Q: 脚本执行失败怎么办？**
A: 检查文件权限，确保脚本有执行权限：`chmod +x doc/brand-migration-script.sh`

**Q: 验证脚本报告失败怎么办？**
A: 查看具体的失败项目，手动检查和修复相关文件

**Q: 构建失败怎么办？**
A: 检查是否有语法错误或配置问题，查看构建日志定位问题

**Q: 发现遗漏的品牌引用怎么办？**
A: 手动搜索和替换，然后重新运行验证脚本

### 回滚方案
如果迁移出现问题，可以通过以下方式回滚：

```bash
# 方法1：使用 Git 回滚
git checkout main
git branch -D feature/brand-migration-appsolve

# 方法2：使用备份恢复
cp -r ../backup-YYYYMMDD-HHMMSS/* .
```

## 📞 支持

如果在迁移过程中遇到问题：

1. 首先查看本文档的故障排除部分
2. 检查 `brand-migration-technical-plan.md` 中的详细说明
3. 使用验证脚本诊断问题
4. 如果问题持续存在，请保留错误日志和相关文件以便分析

## 📈 后续优化

迁移完成后的建议优化：

1. **SEO 优化**：更新搜索引擎相关的元数据
2. **性能监控**：确保品牌变更不影响应用性能
3. **用户体验**：收集用户反馈，优化品牌展示
4. **文档维护**：保持技术文档和品牌指南的同步

---

**最后更新**: 2025-01-15
**版本**: 1.0.0
**维护者**: Development Team
