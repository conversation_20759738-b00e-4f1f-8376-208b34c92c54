#!/bin/bash

# ShipAny to AppSolve Brand Migration Script
# This script automates the brand name replacement process
# 
# Usage: ./brand-migration-script.sh
# 
# IMPORTANT: 
# 1. Make sure to backup your code before running this script
# 2. Review all changes before committing
# 3. Test thoroughly after running the script

set -e  # Exit on any error

echo "🚀 Starting ShipAny to AppSolve brand migration..."
echo "⚠️  Make sure you have backed up your code!"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    exit 1
fi

# Create backup
echo "📦 Creating backup..."
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
cp -r . "../$BACKUP_DIR"
echo "✅ Backup created at ../$BACKUP_DIR"

# Function to replace text in files
replace_in_file() {
    local file="$1"
    local search="$2"
    local replace="$3"
    
    if [[ -f "$file" ]]; then
        if grep -q "$search" "$file"; then
            echo "  📝 Updating $file"
            sed -i.bak "s|$search|$replace|g" "$file"
            rm "$file.bak" 2>/dev/null || true
        fi
    fi
}

# Function to replace text in all files of a directory
replace_in_directory() {
    local dir="$1"
    local search="$2"
    local replace="$3"
    local extensions="$4"
    
    if [[ -d "$dir" ]]; then
        echo "🔍 Processing directory: $dir"
        find "$dir" -type f \( $extensions \) -exec grep -l "$search" {} \; | while read -r file; do
            echo "  📝 Updating $file"
            sed -i.bak "s|$search|$replace|g" "$file"
            rm "$file.bak" 2>/dev/null || true
        done
    fi
}

echo ""
echo "🔧 Phase 1: Core Configuration Files"
echo "=================================="

# Update package.json
echo "📦 Updating package.json..."
replace_in_file "package.json" "shipany-template-one" "appsolve-template-one"
replace_in_file "package.json" "https://shipany.ai" "https://appsolve.ai"
replace_in_file "package.json" "shipany-template-one:latest" "appsolve-template-one:latest"

# Update environment files
echo "🔧 Updating environment configuration..."
replace_in_file ".env.example" "ShipAny" "AppSolve"
replace_in_file "wrangler.toml.example" "shipany-template-one" "appsolve-template-one"
replace_in_file "wrangler.toml.example" "Appsolve" "AppSolve"

# Update README.md
echo "📖 Updating README.md..."
replace_in_file "README.md" "ShipAny Template One" "AppSolve Template One"
replace_in_file "README.md" "Ship Any AI SaaS Startups in hours" "Build Any AI SaaS Startups in hours"
replace_in_file "README.md" "shipanyai/shipany-template-one" "appsolveai/appsolve-template-one"
replace_in_file "README.md" "my-shipany-project" "my-appsolve-project"
replace_in_file "README.md" "https://shipany.ai" "https://appsolve.ai"
replace_in_file "README.md" "docs.shipany.ai" "docs.appsolve.ai"
replace_in_file "README.md" "ShipAny" "AppSolve"

echo ""
echo "🌐 Phase 2: Internationalization Content"
echo "======================================="

# Update English messages
echo "🇺🇸 Updating English content..."
replace_in_file "src/i18n/messages/en.json" "Ship Any AI SaaS Startups in hours | ShipAny" "Build Any AI SaaS Startups in hours | AppSolve"
replace_in_file "src/i18n/messages/en.json" "ShipAny is a NextJS boilerplate" "AppSolve is a NextJS boilerplate"
replace_in_file "src/i18n/messages/en.json" "ShipAny, AI SaaS Boilerplate" "AppSolve, AI SaaS Boilerplate"

# Update Chinese messages
echo "🇨🇳 Updating Chinese content..."
replace_in_file "src/i18n/messages/zh.json" "几小时内构建任何 AI SaaS 创业项目 | ShipAny" "几小时内构建任何 AI SaaS 创业项目 | AppSolve"
replace_in_file "src/i18n/messages/zh.json" "ShipAny 是一个用于构建" "AppSolve 是一个用于构建"
replace_in_file "src/i18n/messages/zh.json" "ShipAny, AI SaaS 模板" "AppSolve, AI SaaS 模板"

# Update landing pages
echo "🏠 Updating landing page content..."
replace_in_directory "src/i18n/pages/landing" "Ship Any AI Startups" "Build Any AI Startups" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "Ship Any" "Build Any" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "ShipAny is built on" "AppSolve is built on" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "How to Launch with ShipAny" "How to Launch with AppSolve" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "about ShipAny" "about AppSolve" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "关于 ShipAny" "关于 AppSolve" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "ShipAny 建立在" "AppSolve 建立在" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "ShipAny 是一个" "AppSolve 是一个" "-name '*.json'"
replace_in_directory "src/i18n/pages/landing" "使用 ShipAny" "使用 AppSolve" "-name '*.json'"

# Update showcase pages
echo "🎨 Updating showcase content..."
replace_in_directory "src/i18n/pages/showcase" "built with ShipAny" "built with AppSolve" "-name '*.json'"
replace_in_directory "src/i18n/pages/showcase" "使用 ShipAny 构建" "使用 AppSolve 构建" "-name '*.json'"

# Update pricing pages
echo "💰 Updating pricing content..."
replace_in_directory "src/i18n/pages/pricing" "Get ShipAny" "Get AppSolve" "-name '*.json'"
replace_in_directory "src/i18n/pages/pricing" "ShipAny Boilerplate" "AppSolve Boilerplate" "-name '*.json'"
replace_in_directory "src/i18n/pages/pricing" "Ship Any AI SaaS" "Build Any AI SaaS" "-name '*.json'"

echo ""
echo "📄 Phase 3: Legal and Documentation"
echo "==================================="

# Update LICENSE
echo "⚖️  Updating LICENSE..."
replace_in_file "LICENSE" "ShipAny AI SaaS Boilerplate License Agreement" "AppSolve AI SaaS Boilerplate License Agreement"
replace_in_file "LICENSE" "ShipAny (\"Licensor\")" "AppSolve (\"Licensor\")"
replace_in_file "LICENSE" "ShipAny grants you" "AppSolve grants you"
replace_in_file "LICENSE" "Remove ShipAny attribution" "Remove AppSolve attribution"
replace_in_file "LICENSE" "ShipAny" "AppSolve"

# Update privacy policy
echo "🔒 Updating privacy policy..."
if [[ -f "src/app/(legal)/privacy-policy/page.mdx" ]]; then
    replace_in_file "src/app/(legal)/privacy-policy/page.mdx" "Privacy Policy for ShipAny" "Privacy Policy for AppSolve"
    replace_in_file "src/app/(legal)/privacy-policy/page.mdx" "Welcome to ShipAny" "Welcome to AppSolve"
    replace_in_file "src/app/(legal)/privacy-policy/page.mdx" "ShipAny" "AppSolve"
fi

# Update marketing documents
echo "📈 Updating marketing documents..."
replace_in_directory "doc/mkt" "Picadabra iOS Template" "AppSolve iOS Template" "-name '*.md'"
replace_in_directory "doc/mkt" "ShipAny" "AppSolve" "-name '*.md'"

echo ""
echo "🔍 Phase 4: Additional Checks"
echo "============================="

# Check for remaining references
echo "🔎 Checking for remaining 'shipany' references..."
REMAINING_SHIPANY=$(find . -type f \( -name "*.json" -o -name "*.md" -o -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.mdx" \) -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./backup-*/*" -exec grep -l -i "shipany" {} \; 2>/dev/null || true)

if [[ -n "$REMAINING_SHIPANY" ]]; then
    echo "⚠️  Found remaining 'shipany' references in:"
    echo "$REMAINING_SHIPANY"
    echo ""
    echo "Please review these files manually."
else
    echo "✅ No remaining 'shipany' references found."
fi

# Check for domain references
echo "🌐 Checking for domain references..."
DOMAIN_REFS=$(find . -type f \( -name "*.json" -o -name "*.md" -o -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.mdx" \) -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./backup-*/*" -exec grep -l "shipany\.ai" {} \; 2>/dev/null || true)

if [[ -n "$DOMAIN_REFS" ]]; then
    echo "⚠️  Found remaining 'shipany.ai' references in:"
    echo "$DOMAIN_REFS"
    echo ""
    echo "Please update these domain references manually."
else
    echo "✅ No remaining 'shipany.ai' references found."
fi

echo ""
echo "✨ Migration Complete!"
echo "===================="
echo ""
echo "📋 Next Steps:"
echo "1. Review all changes carefully"
echo "2. Update logo and favicon files in public/ directory"
echo "3. Test the application locally: pnpm dev"
echo "4. Run build test: pnpm build"
echo "5. Update any external service configurations"
echo "6. Commit changes and test deployment"
echo ""
echo "📦 Backup location: ../$BACKUP_DIR"
echo ""
echo "🎉 Brand migration from ShipAny to AppSolve completed successfully!"
