# ShipAny 到 AppSolve 品牌迁移技术方案

## 项目概述

本文档详细描述了将 ShipAny 品牌名称全面迁移到 AppSolve 的技术实施方案。基于对现有项目架构的深入分析，制定了系统性的迁移策略，确保品牌变更的完整性和一致性。

## 项目架构分析

### 技术栈
- **前端框架**: Next.js 15.2.3 (React 19.0.0)
- **样式系统**: TailwindCSS 4.1.4 + Shadcn/UI
- **国际化**: next-intl 4.1.0 (支持中英文)
- **数据库**: PostgreSQL (Drizzle ORM)
- **认证系统**: NextAuth 5.0.0-beta.25
- **部署平台**: Vercel / Cloudflare Workers
- **包管理**: pnpm

### 项目结构
```
shipany-template/
├── src/
│   ├── app/                 # Next.js App Router
│   ├── components/          # React 组件
│   ├── i18n/               # 国际化配置和内容
│   │   ├── messages/       # 翻译文件 (en.json, zh.json)
│   │   └── pages/          # 页面级翻译内容
│   ├── lib/                # 工具函数
│   ├── services/           # 业务逻辑
│   └── types/              # TypeScript 类型定义
├── public/                 # 静态资源
├── doc/                    # 文档目录
└── 配置文件 (package.json, README.md, etc.)
```

## 品牌名称使用情况分析

### 1. 配置文件层面
- **package.json**: 项目名称、主页URL、Docker镜像名
- **.env.example**: NEXT_PUBLIC_PROJECT_NAME 环境变量
- **wrangler.toml.example**: Cloudflare Workers 配置
- **README.md**: 项目介绍、GitHub链接、部署说明

### 2. 国际化内容层面
- **src/i18n/messages/**: 元数据标题、描述、关键词
- **src/i18n/pages/landing/**: 首页内容、Hero区域、功能介绍
- **src/i18n/pages/showcase/**: 案例展示页面
- **src/i18n/pages/pricing/**: 定价页面产品名称

### 3. 法律文档层面
- **LICENSE**: 许可协议中的品牌名称
- **src/app/(legal)/privacy-policy/**: 隐私政策

### 4. 营销文档层面
- **doc/mkt/**: 营销相关文档和定价策略

## 迁移实施方案

### 阶段一：核心配置文件迁移 (优先级：高)

#### 1.1 项目基础配置
- [ ] **package.json**
  - 修改 `name`: "shipany-template-one" → "appsolve-template-one"
  - 修改 `homepage`: "https://shipany.ai" → "https://appsolve.ai"
  - 修改 Docker 构建脚本中的镜像名称

- [ ] **环境变量配置**
  - **.env.example**: NEXT_PUBLIC_PROJECT_NAME = "ShipAny" → "AppSolve"
  - **wrangler.toml.example**: 
    - name: "shipany-template-one" → "appsolve-template-one"
    - NEXT_PUBLIC_PROJECT_NAME = "Appsolve" → "AppSolve" (统一大小写)

#### 1.2 部署配置
- [ ] **README.md**: 更新所有 GitHub 链接、部署按钮、社区链接
- [ ] **Dockerfile**: 更新镜像标签名称
- [ ] **vercel.json**: 检查是否有品牌相关配置

### 阶段二：国际化内容迁移 (优先级：高)

#### 2.1 核心元数据
- [ ] **src/i18n/messages/en.json**
  - metadata.title: "Ship Any AI SaaS Startups in hours | ShipAny" → "Build Any AI SaaS Startups in hours | AppSolve"
  - metadata.description: 更新品牌名称和描述
  - metadata.keywords: 更新关键词

- [ ] **src/i18n/messages/zh.json**
  - 对应的中文翻译更新

#### 2.2 首页内容
- [ ] **src/i18n/pages/landing/en.json**
  - hero.title: "Ship Any AI Startups" → "Build Any AI Startups"
  - hero.highlight_text: "Ship Any" → "Build Any"
  - hero.description: 更新品牌描述
  - branding.title: "ShipAny is built on..." → "AppSolve is built on..."
  - usage.title: "How to Launch with ShipAny" → "How to Launch with AppSolve"
  - faq 部分的所有品牌引用

- [ ] **src/i18n/pages/landing/zh.json**
  - 对应的中文内容更新

#### 2.3 其他页面内容
- [ ] **src/i18n/pages/showcase/**: 案例展示页面标题和描述
- [ ] **src/i18n/pages/pricing/**: 产品名称更新

### 阶段三：法律和文档迁移 (优先级：中)

#### 3.1 法律文档
- [ ] **LICENSE**: 
  - 标题: "ShipAny AI SaaS Boilerplate License Agreement" → "AppSolve AI SaaS Boilerplate License Agreement"
  - 正文中所有 "ShipAny" 引用

- [ ] **src/app/(legal)/privacy-policy/page.mdx**
  - 隐私政策中的品牌名称更新

#### 3.2 营销文档
- [ ] **doc/mkt/** 目录下的所有文档
  - 定价策略文档中的品牌名称
  - 营销材料中的品牌引用

### 阶段四：静态资源和视觉元素 (优先级：中)

#### 4.1 图片和Logo
- [ ] **public/logo.png**: 更新为 AppSolve Logo
- [ ] **public/favicon.ico**: 更新网站图标
- [ ] **preview.png**: 更新项目预览图

#### 4.2 其他静态资源
- [ ] 检查 **public/imgs/** 目录下是否有品牌相关图片
- [ ] 更新 **public/sitemap.xml** 中的URL引用

### 阶段五：代码层面检查 (优先级：低)

#### 5.1 源代码扫描
- [ ] 搜索源代码中硬编码的 "shipany" 或 "ShipAny" 字符串
- [ ] 检查组件中是否有品牌名称的直接引用
- [ ] 验证所有品牌名称都通过环境变量或国际化系统管理

#### 5.2 URL和链接检查
- [ ] 检查代码中的外部链接 (shipany.ai → appsolve.ai)
- [ ] 更新 API 端点中的品牌相关路径
- [ ] 验证重定向规则

## 实施优先级和时间安排

### 第1天：核心配置 (2-3小时)
1. 更新 package.json 和环境变量配置
2. 更新 README.md 和部署配置
3. 测试本地开发环境

### 第2天：国际化内容 (4-5小时)
1. 更新英文内容 (en.json 和相关页面)
2. 更新中文内容 (zh.json 和相关页面)
3. 测试多语言显示效果

### 第3天：文档和资源 (2-3小时)
1. 更新法律文档和营销材料
2. 替换静态资源 (Logo、图标等)
3. 全面测试和验证

## 风险评估和注意事项

### 高风险项
1. **环境变量配置错误**: 可能导致应用无法正常启动
2. **国际化键值错误**: 可能导致页面显示异常
3. **部署配置问题**: 可能影响生产环境部署

### 缓解措施
1. **备份策略**: 迁移前创建完整的代码备份
2. **分步测试**: 每个阶段完成后进行功能测试
3. **回滚计划**: 准备快速回滚到原始状态的方案

### 测试检查清单
- [ ] 本地开发环境正常启动
- [ ] 多语言切换功能正常
- [ ] 所有页面品牌名称显示正确
- [ ] 外部链接指向正确
- [ ] 部署流程无异常

## 后续维护建议

1. **品牌一致性检查**: 建立定期检查机制，确保新增内容使用正确的品牌名称
2. **文档同步更新**: 确保技术文档和营销材料保持同步
3. **SEO优化**: 更新搜索引擎优化相关的元数据和关键词
4. **域名迁移**: 如需要，准备域名迁移和重定向策略

## 总结

本迁移方案采用分阶段、低风险的实施策略，确保品牌变更的完整性和系统稳定性。通过系统性的检查和测试，可以实现从 ShipAny 到 AppSolve 的平滑过渡。

预计总工作量：8-10小时
建议实施周期：3个工作日
