# ShipAny → AppSolve 品牌迁移实施检查清单

## 迁移前准备

### 备份和环境准备
- [ ] 创建完整的代码备份
- [ ] 确认当前分支状态干净 (无未提交的更改)
- [ ] 创建新的功能分支 `feature/brand-migration-appsolve`
- [ ] 准备 AppSolve 的新 Logo 和图标文件

## 阶段一：核心配置文件迁移

### package.json 更新
- [ ] 修改 `name`: "shipany-template-one" → "appsolve-template-one"
- [ ] 修改 `homepage`: "https://shipany.ai" → "https://appsolve.ai"
- [ ] 更新 `docker:build` 脚本中的镜像名称

### 环境变量配置
- [ ] **.env.example**
  - [ ] NEXT_PUBLIC_PROJECT_NAME = "ShipAny" → "AppSolve"
- [ ] **wrangler.toml.example**
  - [ ] name = "shipany-template-one" → "appsolve-template-one"
  - [ ] NEXT_PUBLIC_PROJECT_NAME = "Appsolve" → "AppSolve"
  - [ ] 检查其他 URL 引用

### 部署和构建配置
- [ ] **README.md**
  - [ ] 项目标题: "ShipAny Template One" → "AppSolve Template One"
  - [ ] 描述中的品牌名称更新
  - [ ] GitHub 克隆链接更新
  - [ ] Vercel 部署按钮中的 URL 参数更新
  - [ ] 社区链接更新 (shipany.ai → appsolve.ai)
  - [ ] 文档链接更新 (docs.shipany.ai → docs.appsolve.ai)

- [ ] **Dockerfile**
  - [ ] 镜像标签名称更新

## 阶段二：国际化内容迁移

### 英文内容 (src/i18n/messages/en.json)
- [ ] **metadata 部分**
  - [ ] title: "Ship Any AI SaaS Startups in hours | ShipAny" → "Build Any AI SaaS Startups in hours | AppSolve"
  - [ ] description: 更新品牌名称和描述
  - [ ] keywords: "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate" → "AppSolve, AI SaaS Boilerplate, NextJS Boilerplate"

### 中文内容 (src/i18n/messages/zh.json)
- [ ] **metadata 部分**
  - [ ] title: "几小时内构建任何 AI SaaS 创业项目 | ShipAny" → "几小时内构建任何 AI SaaS 创业项目 | AppSolve"
  - [ ] description: 更新品牌名称
  - [ ] keywords: 更新关键词

### 首页内容 - 英文 (src/i18n/pages/landing/en.json)
- [ ] **hero 部分**
  - [ ] title: "Ship Any AI Startups in hours, not days" → "Build Any AI Startups in hours, not days"
  - [ ] highlight_text: "Ship Any" → "Build Any"
  - [ ] description: "ShipAny is a NextJS boilerplate..." → "AppSolve is a NextJS boilerplate..."

- [ ] **branding 部分**
  - [ ] title: "ShipAny is built on the shoulders of giants" → "AppSolve is built on the shoulders of giants"

- [ ] **usage 部分**
  - [ ] title: "How to Launch with ShipAny" → "How to Launch with AppSolve"

- [ ] **faq 部分**
  - [ ] title: "Frequently Asked Questions about ShipAny" → "Frequently Asked Questions about AppSolve"
  - [ ] 所有问答中的 "ShipAny" → "AppSolve"

### 首页内容 - 中文 (src/i18n/pages/landing/zh.json)
- [ ] **branding 部分**
  - [ ] title: "ShipAny 建立在巨人的肩膀上" → "AppSolve 建立在巨人的肩膀上"

- [ ] **faq 部分**
  - [ ] title: "关于 ShipAny 的常见问题" → "关于 AppSolve 的常见问题"
  - [ ] 所有问答中的 "ShipAny" → "AppSolve"

### 其他页面内容
- [ ] **src/i18n/pages/showcase/en.json**
  - [ ] title: "AI SaaS Startups built with ShipAny" → "AI SaaS Startups built with AppSolve"

- [ ] **src/i18n/pages/showcase/zh.json**
  - [ ] title: "使用 ShipAny 构建的 AI SaaS 创业项目" → "使用 AppSolve 构建的 AI SaaS 创业项目"

- [ ] **src/i18n/pages/pricing/en.json**
  - [ ] 检查产品名称中的品牌引用
  - [ ] button.title: "Get ShipAny" → "Get AppSolve"
  - [ ] product_name: "ShipAny Boilerplate Premium" → "AppSolve Boilerplate Premium"

## 阶段三：法律和文档迁移

### 法律文档
- [ ] **LICENSE**
  - [ ] 标题: "ShipAny AI SaaS Boilerplate License Agreement" → "AppSolve AI SaaS Boilerplate License Agreement"
  - [ ] 第7行: "ShipAny ("Licensor")" → "AppSolve ("Licensor")"
  - [ ] 第13行: "ShipAny grants you a Personal License" → "AppSolve grants you a Personal License"
  - [ ] 第22行: "ShipAny grants you a Team License" → "AppSolve grants you a Team License"
  - [ ] 第37行: "Remove ShipAny attribution" → "Remove AppSolve attribution"
  - [ ] 所有其他 "ShipAny" 引用

- [ ] **src/app/(legal)/privacy-policy/page.mdx**
  - [ ] 标题: "Privacy Policy for ShipAny" → "Privacy Policy for AppSolve"
  - [ ] 第5行: "Welcome to ShipAny" → "Welcome to AppSolve"
  - [ ] 所有正文中的品牌名称引用

### 营销文档
- [ ] **doc/mkt/Pricing-Strategy-and-Delivery-Plan.md**
  - [ ] 标题: "Appsolve iOS 模板定价策略与交付方案" (已经是 Appsolve，检查一致性)

- [ ] **doc/mkt/Pricing-Strategy-and-Delivery-Plan-EN.md**
  - [ ] 标题: "Picadabra iOS Template..." → "AppSolve iOS Template..."
  - [ ] 正文中的品牌名称统一

- [ ] **doc/mkt/Developer-Marketing-README.md**
  - [ ] 检查品牌名称一致性

- [ ] **doc/mkt/Developer-Marketing-README-EN.md**
  - [ ] 检查品牌名称一致性

## 阶段四：静态资源更新

### 图片和Logo
- [ ] **public/logo.png**
  - [ ] 替换为 AppSolve Logo

- [ ] **public/favicon.ico**
  - [ ] 更新为 AppSolve 图标

- [ ] **preview.png**
  - [ ] 更新项目预览图 (如果包含品牌元素)

### 其他静态文件
- [ ] **public/sitemap.xml**
  - [ ] 检查是否有 URL 引用需要更新

- [ ] **public/robots.txt**
  - [ ] 检查是否有品牌相关内容

## 阶段五：代码层面检查

### 源代码扫描
- [ ] 全局搜索 "shipany" (不区分大小写)
- [ ] 全局搜索 "ShipAny"
- [ ] 检查组件中的硬编码品牌名称
- [ ] 检查 API 路径中的品牌引用

### URL和链接检查
- [ ] 搜索 "shipany.ai" 域名引用
- [ ] 搜索 "docs.shipany.ai" 引用
- [ ] 检查重定向配置
- [ ] 验证外部链接的正确性

## 测试验证

### 本地开发测试
- [ ] 启动本地开发服务器 (`pnpm dev`)
- [ ] 验证首页显示正确的品牌名称
- [ ] 测试语言切换功能
- [ ] 检查所有主要页面的品牌显示

### 功能测试
- [ ] 用户注册/登录流程
- [ ] 支付流程 (如果涉及品牌名称)
- [ ] 邮件模板 (如果有)
- [ ] 错误页面显示

### 构建测试
- [ ] 执行生产构建 (`pnpm build`)
- [ ] 检查构建输出中的品牌名称
- [ ] 验证静态资源正确引用

### 部署测试
- [ ] Vercel 部署测试
- [ ] Cloudflare Workers 部署测试 (如果使用)
- [ ] 验证生产环境的品牌显示

## 最终检查

### 完整性检查
- [ ] 所有 "ShipAny" 引用已更新为 "AppSolve"
- [ ] 所有 "shipany" 引用已更新为 "appsolve"
- [ ] URL 和域名引用正确
- [ ] 静态资源正确更新

### 文档更新
- [ ] 更新技术文档
- [ ] 更新部署说明
- [ ] 更新开发者指南

### 提交和发布
- [ ] 提交所有更改到功能分支
- [ ] 创建 Pull Request
- [ ] 代码审查
- [ ] 合并到主分支
- [ ] 创建发布标签

## 注意事项

1. **备份重要**: 确保在开始迁移前有完整备份
2. **分步测试**: 每个阶段完成后都要进行测试
3. **一致性检查**: 确保所有地方的品牌名称大小写一致
4. **外部依赖**: 检查是否有外部服务需要更新配置
5. **SEO影响**: 考虑品牌变更对搜索引擎优化的影响

## 预计工作量

- **阶段一**: 1-2小时
- **阶段二**: 3-4小时  
- **阶段三**: 1-2小时
- **阶段四**: 1小时
- **阶段五**: 1-2小时
- **测试验证**: 1-2小时

**总计**: 8-13小时
