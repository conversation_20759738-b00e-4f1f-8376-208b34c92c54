{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "AppSolve", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "nav": {"items": [{"title": "功能特点", "url": "/#feature", "icon": "RiSparkling2Line"}, {"title": "定价", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "文档", "url": "/docs", "icon": "RiBookOpenLine"}]}, "buttons": [{"title": "获取 AppSolve", "url": "https://appsolve.ai", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "几天内启动你的爆款 iOS 应用，而不是几个月", "highlight_text": "iOS 应用", "description": "AI 驱动的 iOS 应用模板，配备 SwiftUI、Firebase 和 RevenueCat。<br/>跳过几个月的开发时间，使用生产级功能。", "announcement": {"label": "2025", "title": "🎉 新年快乐", "url": "/#pricing"}, "tip": "🎁 2025年前五折优惠", "buttons": [{"title": "立即开始", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "加入 Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "branding": {"title": "AppSolve 建立在巨人的肩膀上", "items": [{"title": "SwiftUI", "image": {"src": "/imgs/logos/swiftui.svg", "alt": "SwiftUI"}}, {"title": "Firebase", "image": {"src": "/imgs/logos/firebase.svg", "alt": "Firebase"}}, {"title": "RevenueCat", "image": {"src": "/imgs/logos/revenuecat.svg", "alt": "RevenueCat"}}, {"title": "Combine", "image": {"src": "/imgs/logos/combine.svg", "alt": "Combine"}}, {"title": "Xcode", "image": {"src": "/imgs/logos/xcode.svg", "alt": "Xcode"}}]}, "introduce": {"name": "introduce", "title": "什么是 AppSolve", "label": "介绍", "description": "AppSolve 是一个用于构建 AI 驱动应用的 iOS 应用模板。完整集成身份验证、支付和 AI 功能。", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "即用型 iOS 模板", "description": "生产就绪的 iOS 应用模板，具备 AI 功能，快速启动您的移动应用。", "icon": "RiAppStoreFill"}, {"title": "完整后端设置", "description": "Firebase 后端，包含身份验证、数据库和云函数，随时可扩展。", "icon": "RiDatabase2Line"}, {"title": "App Store 就绪", "description": "使用生产就绪代码，几天内在 App Store 上发布您的 AI iOS 应用。", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "为什么选择 AppSolve", "label": "优势", "description": "获取启动 AI iOS 应用所需的一切 - 从原生模板到 App Store 部署。", "items": [{"title": "完整 iOS 框架", "description": "使用 SwiftUI、Firebase 身份验证、RevenueCat 支付和 AI 集成构建 - 一切开箱即用。", "icon": "RiAppStoreFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "精美 UI 设计", "description": "美观的 UI 设计，配备现代 iOS 组件和动画。", "icon": "RiPaletteLine", "image": {"src": "/imgs/features/9.png", "alt": "UI View"}}, {"title": "丰富的 iOS 模板", "description": "选择各种 AI iOS 应用模板 - 图像生成、AI 助手等。", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "iOS 开发支持", "description": "获得专门的 iOS 开发支持并加入我们的开发者社区，确保成功发布到 App Store。", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "如何使用 AppSolve 启动项目", "description": "通过三个简单步骤启动您的 AI iOS 应用：", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "获取 AppSolve", "description": "一次性付款购买 AppSolve。查收邮件获取 Xcode 项目和文档。", "image": {"src": "/imgs/features/5.png"}}, {"title": "在 Xcode 中打开", "description": "阅读文档并在 Xcode 中打开 AppSolve 项目。开始构建您的 AI iOS 应用。", "image": {"src": "/imgs/features/6.png"}}, {"title": "定制您的应用", "description": "使用您的品牌和 AI 功能修改 iOS 模板。添加您的特定功能。", "image": {"src": "/imgs/features/7.png"}}, {"title": "提交到 App Store", "description": "将您的应用提交到 App Store，立即开始为全球用户提供服务。", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "AppSolve 核心功能", "description": "快速高效启动 AI iOS 应用所需的一切。", "items": [{"title": "SwiftUI 模板", "description": "生产就绪的 SwiftUI 模板，具备现代 iOS 设计模式和组件。", "icon": "RiAppStoreFill"}, {"title": "身份验证和支付", "description": "Apple ID、Google 登录、邮箱/OTP 身份验证和 RevenueCat 订阅管理。", "icon": "RiKey2Fill"}, {"title": "Firebase 后端", "description": "完整的 Firebase 集成，包含 Firestore、云存储和云函数。", "icon": "RiDatabase2Line"}, {"title": "App Store 就绪", "description": "预配置 App Store 提交所需的所有设置和资源。", "icon": "RiCloudy2Fill"}, {"title": "应用分析", "description": "集成 Firebase Analytics 和 RevenueCat 指标来追踪增长。", "icon": "RiBarChart2Line"}, {"title": "AI 集成", "description": "预配置 AI 功能，包含 OpenAI 集成和应用内购买积分。", "icon": "RiRobot2Line"}]}, "stats": {"disabled": true, "name": "stats", "label": "统计", "title": "用户喜爱 AppSolve", "description": "因为它易于使用且快速发布。", "icon": "FaRegHeart", "items": [{"title": "信任", "label": "99+", "description": "客户"}, {"title": "内置", "label": "20+", "description": "组件"}, {"title": "快速发布", "label": "5", "description": "分钟"}]}, "testimonial": {"disabled": true, "name": "testimonial", "label": "用户评价", "title": "用户如何评价 AppSolve", "description": "听听使用 AppSolve 启动 AI 创业项目的开发者和创始人怎么说。", "icon": "GoThumbsup", "items": [{"title": "陈大卫", "label": "AIWallpaper.shop 创始人", "description": "AppSolve 为我们节省了数月的开发时间。我们仅用 2 天就启动了 AI 壁纸业务，一周内就获得了第一个付费客户！", "image": {"src": "/imgs/users/1.png"}}, {"title": "金瑞秋", "label": "HeyBeauty.ai 技术总监", "description": "预构建的 AI 基础设施是一个游戏规则改变者。我们无需担心架构 - 只需专注于 AI 美容技术并快速上线。", "image": {"src": "/imgs/users/2.png"}}, {"title": "马库斯", "label": "独立开发者", "description": "作为独立开发者，AppSolve 给了我所需的一切 - 身份验证、支付、AI 集成和漂亮的 UI。一个周末就启动了我的 SaaS！", "image": {"src": "/imgs/users/3.png"}}, {"title": "索菲亚", "label": "Melodisco CEO", "description": "这些模板可直接用于生产且高度可定制。我们用几小时而不是几个月就建立了 AI 音乐平台。上市时间令人难以置信！", "image": {"src": "/imgs/users/4.png"}}, {"title": "詹姆斯", "label": "GPTs.works 技术主管", "description": "AppSolve 的基础设施非常稳固。我们从 0 扩展到 1 万用户都没碰后端。这是我们 AI 创业最好的投资。", "image": {"src": "/imgs/users/5.png"}}, {"title": "张安娜", "label": "创业者", "description": "从想法到上线只用了 3 天！AppSolve 的模板和部署工具让我们能够以令人难以置信的速度测试 AI 业务概念。", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于 AppSolve 的常见问题", "description": "还有其他问题？通过 Discord 或电子邮件联系我们。", "items": [{"title": "AppSolve 究竟是什么，它是如何工作的？", "description": "AppSolve 是一个专门为构建 AI 驱动 iOS 应用设计的综合性 iOS 应用模板。它提供即用型 SwiftUI 模板、Firebase 后端集成和 App Store 部署配置，帮助您在几天内而不是几个月内启动 AI iOS 应用。"}, {"title": "使用 AppSolve 需要高级 iOS 开发技能吗？", "description": "虽然基本的 iOS 开发知识会有帮助，但 AppSolve 设计得非常开发者友好。我们的 SwiftUI 模板和详细文档使您即使不是 AI 或 Firebase 专家也能轻松入门。"}, {"title": "我可以用 AppSolve 构建什么类型的 AI iOS 应用？", "description": "AppSolve 支持广泛的 AI iOS 应用，从图像生成到 AI 助手工具。我们的模板涵盖流行用例，如 AI 聊天机器人、内容生成器、图像处理应用等。"}, {"title": "使用 AppSolve 通常需要多长时间才能发布到 App Store？", "description": "使用 AppSolve，您可以在几天内完成工作原型，并在一周内完成生产就绪的 iOS 应用。我们的预配置 Firebase 后端和 App Store 就绪设置显著缩短了传统的数月开发周期。"}, {"title": "AppSolve 的技术栈包括什么？", "description": "AppSolve 提供完整的 iOS 技术栈，包括 SwiftUI UI 框架、Firebase 身份验证和数据库、RevenueCat 支付处理、AI 集成和 App Store 部署配置。一切都按照 iOS 最佳实践预先配置。"}, {"title": "我可以自定义模板以匹配我的品牌吗？", "description": "当然可以！所有 AppSolve 模板都完全可定制。您可以修改设计、功能和功能性以匹配您的品牌标识和特定业务需求，同时保持强大的底层基础设施。"}]}, "cta": {"name": "cta", "title": "启动您的第一个 AI iOS 应用", "description": "从这里开始，使用 AppSolve 启动。", "buttons": [{"title": "获取 AppSolve", "url": "https://appsolve.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "阅读文档", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "AppSolve", "description": "AppSolve 是一个用于构建 AI 驱动 iOS 应用的 SwiftUI 模板。通过丰富的模板和组件快速启动。", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "copyright": "© 2025 • AppSolve 保留所有权利。", "nav": {"items": [{"title": "关于", "children": [{"title": "功能特点", "url": "/#feature", "target": "_self"}, {"title": "定价", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "组件", "url": "https://appsolve.ai/components", "target": "_blank"}, {"title": "模板", "url": "https://appsolve.ai/templates", "target": "_blank"}]}, {"title": "友情链接", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}