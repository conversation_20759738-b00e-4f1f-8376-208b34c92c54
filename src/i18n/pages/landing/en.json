{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "AppSolve", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "nav": {"items": [{"title": "Features", "url": "/#feature", "icon": "RiSparkling2Line"}, {"title": "Pricing", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "Docs", "url": "/docs", "icon": "RiBookOpenLine"}]}, "buttons": [{"title": "Get AppSolve", "url": "https://appsolve.ai", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": false, "show_locale": true}, "hero": {"title": "Launch Your Hit iOS App in Days, Not Months", "highlight_text": "iOS App", "description": "AI-powered iOS app template with SwiftUI, Firebase, and RevenueCat.<br/>Skip months of development with production-ready features.", "announcement": {"label": "2025", "title": "🎉 Happy New Year", "url": "/#pricing"}, "tip": "🎁 50% off before 2025", "buttons": [{"title": "Get Started", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "Join <PERSON>", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "branding": {"title": "AppSolve is built on the shoulders of giants", "items": [{"title": "SwiftUI", "image": {"src": "/imgs/logos/swiftui.svg", "alt": "SwiftUI"}}, {"title": "Firebase", "image": {"src": "/imgs/logos/firebase.svg", "alt": "Firebase"}}, {"title": "RevenueCat", "image": {"src": "https://www.revenuecat.com/static/logo-10968976818e28fd8eee91f66d535d9a.svg", "alt": "RevenueCat"}}, {"title": "Cloudflare", "image": {"src": "https://cf-assets.www.cloudflare.com/dzlvafdwdttg/69wNwfiY5mFmgpd9eQFW6j/d5131c08085a977aa70f19e7aada3fa9/1pixel-down__1_.svg", "alt": "Cloudflare"}}]}, "introduce": {"name": "introduce", "title": "What is AppSolve", "label": "Introduce", "description": "AppSolve is an iOS app template for building AI-powered applications. Complete with authentication, payments, and AI features.", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "Ready-to-use iOS Templates", "description": "Production-ready iOS app templates with AI capabilities to quickly launch your mobile app.", "icon": "RiAppStoreFill"}, {"title": "Complete Backend Setup", "description": "Firebase backend with authentication, database, and cloud functions ready to scale.", "icon": "RiDatabase2Line"}, {"title": "App Store Ready", "description": "Launch your AI iOS app on the App Store in days with production-ready code.", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose AppSolve", "label": "Benefits", "description": "Get everything you need to launch your AI iOS app - from native templates to App Store deployment.", "items": [{"title": "Complete iOS Framework", "description": "Built with SwiftUI, Firebase auth, RevenueCat payments, and AI integration - everything works out of the box.", "icon": "RiAppStoreFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "Polish UI Design", "description": "Beautiful UI design with modern iOS components and animations.", "icon": "RiPaletteLine", "image": {"src": "/imgs/features/9.png", "alt": "UI View"}}, {"title": "Rich iOS Templates", "description": "Choose from various AI iOS app templates - image generation, AI assistants, and more.", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "iOS Development Support", "description": "Get dedicated iOS development support and join our developer community for successful App Store launch.", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "How to Launch with AppSolve", "description": "Get your AI iOS app running in three simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Get AppSolve", "description": "Buy AppSolve with a one-time payment. Check your email for the Xcode project and documentation.", "image": {"src": "/imgs/features/5.png"}}, {"title": "Open in Xcode", "description": "Read the documentation and open the AppSolve project in Xcode. Start building your AI iOS app.", "image": {"src": "/imgs/features/6.png"}}, {"title": "Customize Your App", "description": "Modify the iOS template with your branding and AI features. Add your specific functionality.", "image": {"src": "/imgs/features/7.png"}}, {"title": "Submit to App Store", "description": "Submit your app to the App Store and start serving users worldwide immediately.", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "Key Features of AppSolve", "description": "Everything you need to launch your AI iOS app quickly and efficiently.", "items": [{"title": "SwiftUI Templates", "description": "Production-ready SwiftUI templates with modern iOS design patterns and components.", "icon": "RiAppStoreFill"}, {"title": "Authentication & Payments", "description": "Apple ID, Google Sign-In, Email/OTP auth, and RevenueCat subscription management.", "icon": "RiKey2Fill"}, {"title": "Firebase Backend", "description": "Complete Firebase integration with Firestore, Cloud Storage, and Cloud Functions.", "icon": "RiDatabase2Line"}, {"title": "App Store Ready", "description": "Pre-configured for App Store submission with all required settings and assets.", "icon": "RiCloudy2Fill"}, {"title": "App Analytics", "description": "Integrated Firebase Analytics and RevenueCat metrics for tracking growth.", "icon": "RiBarChart2Line"}, {"title": "AI Integration", "description": "Pre-configured AI features with OpenAI integration and in-app purchase credits.", "icon": "RiRobot2Line"}]}, "stats": {"disabled": true, "name": "stats", "label": "Stats", "title": "People Love AppSolve", "description": "for its native performance and fast App Store launch.", "icon": "FaRegHeart", "items": [{"title": "Trusted by", "label": "99+", "description": "Customers"}, {"title": "Built in", "label": "20+", "description": "Components"}, {"title": "Ship Fast in", "label": "5", "description": "Minutes"}]}, "testimonial": {"disabled": true, "name": "testimonial", "label": "Testimonial", "title": "What Users Say About AppSolve", "description": "Hear from developers and founders who launched their AI iOS apps with AppSolve.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of AIWallpaper iOS", "description": "AppSolve saved us months of iOS development time. We launched our AI wallpaper iOS app in just 2 days and reached #1 in Productivity!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "CTO at HeyBeauty iOS", "description": "The pre-built Firebase architecture is a game-changer. We didn't have to worry about backend - just focused on our AI beauty features and shipped fast.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Solo Developer", "description": "As a solo iOS developer, AppSolve gave me everything I needed - auth, payments, AI integration, and native UI. Launched my app in a weekend!", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "CEO of Melodisco iOS", "description": "The SwiftUI templates are production-ready and highly customizable. We built our AI music iOS app in hours instead of months. Incredible time-to-market!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Tech Lead at GPTs iOS", "description": "AppSolve's Firebase infrastructure is rock-solid. We scaled from 0 to 10k iOS users without touching the backend. Best investment for our AI app.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "From idea to App Store in 3 days! AppSolve's iOS templates and submission tools made it possible to test our AI business concept incredibly fast.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About AppSolve", "description": "Have another question? Contact us on Discord or by email.", "items": [{"title": "What exactly is AppSolve and how does it work?", "description": "AppSolve is a comprehensive iOS app template designed specifically for building AI-powered mobile applications. It provides ready-to-use SwiftUI templates, Firebase infrastructure, and App Store deployment tools that help you launch your AI iOS app in days instead of months."}, {"title": "Do I need advanced iOS development skills to use AppSolve?", "description": "While basic Swift and iOS development knowledge is helpful, AppSolve is designed to be developer-friendly. Our SwiftUI templates and documentation make it easy to get started, even if you're not an expert in iOS or Firebase."}, {"title": "What types of AI iOS apps can I build with AppSolve?", "description": "AppSolve supports a wide range of AI iOS applications, from image generation to AI assistants. Our SwiftUI templates cover popular use cases like AI photo editors, content generators, AI chatbots, and more."}, {"title": "How long does it typically take to launch with AppSolve?", "description": "With AppSolve, you can have a working iOS prototype in hours and a production-ready application in days. Our App Store ready templates and pre-configured Firebase infrastructure significantly reduce the traditional months-long iOS development cycle."}, {"title": "What's included in the AppSolve iOS template?", "description": "AppSolve provides a complete iOS app stack including SwiftUI components, Firebase authentication, Firestore database, RevenueCat payments, AI integration, and App Store deployment configuration. Everything is pre-configured following iOS best practices."}, {"title": "Can I customize the iOS templates to match my brand?", "description": "Absolutely! All AppSolve SwiftUI templates are fully customizable. You can modify the design, features, and functionality to match your brand identity and specific app requirements while maintaining the robust Firebase infrastructure."}]}, "cta": {"name": "cta", "title": "Launch your first AI iOS App", "description": "Start from here, ship with AppSolve.", "buttons": [{"title": "Get AppSolve", "url": "https://appsolve.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "Read Document", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "AppSolve", "description": "AppSolve is a NextJS boilerplate for building AI SaaS startups. Ship Fast with a variety of templates and components.", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "copyright": "© 2025 • AppSolve All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.appsolve.ai", "target": "_blank"}, {"title": "Components", "url": "https://appsolve.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://appsolve.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/appsolveai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/appsolveai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}