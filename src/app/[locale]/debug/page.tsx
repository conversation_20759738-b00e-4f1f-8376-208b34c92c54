"use client";

export default function DebugPage() {
  const envVars = {
    NEXT_PUBLIC_AUTH_GOOGLE_ENABLED: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED,
    NEXT_PUBLIC_AUTH_GOOGLE_ID: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID,
    NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED,
    NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Variables Debug</h1>
      <div className="space-y-2">
        {Object.entries(envVars).map(([key, value]) => (
          <div key={key} className="flex gap-4">
            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {key}:
            </span>
            <span className="font-mono text-sm">
              {value || "undefined"}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
