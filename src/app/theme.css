:root {
  /* Modern green color scheme inspired by the image */
  /* Primary vibrant green: #00D084 -> oklch(0.75 0.18 160) */
  /* Secondary teal green: #00C896 -> oklch(0.72 0.16 170) */
  /* Accent emerald: #10B981 -> oklch(0.70 0.15 155) */

  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.02 160);
  --card: oklch(0.98 0.01 160);
  --card-foreground: oklch(0.15 0.02 160);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 160);
  --primary: oklch(0.75 0.18 160); /* Vibrant green */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.72 0.16 170); /* Teal green */
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.96 0.02 160);
  --muted-foreground: oklch(0.45 0.05 160);
  --accent: oklch(0.7 0.15 155); /* Emerald */
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.93 0.03 160);
  --input: oklch(0.98 0.02 160);
  --ring: oklch(0.75 0.18 160);
  --chart-1: oklch(0.75 0.18 160); /* Vibrant green */
  --chart-2: oklch(0.72 0.16 170); /* Teal green */
  --chart-3: oklch(0.7 0.15 155); /* Emerald */
  --chart-4: oklch(0.68 0.14 165); /* Medium green */
  --chart-5: oklch(0.65 0.12 150); /* Forest green */
  --sidebar: oklch(0.98 0.01 160);
  --sidebar-foreground: oklch(0.15 0.02 160);
  --sidebar-primary: oklch(0.75 0.18 160);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.7 0.15 155);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.93 0.03 160);
  --sidebar-ring: oklch(0.75 0.18 160);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Modern green gradient variables */
  --gradient-primary: linear-gradient(135deg, #00d084 0%, #00c896 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(0, 208, 132, 0.1) 0%,
    rgba(0, 200, 150, 0.1) 100%
  );
  --gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(0, 208, 132, 0.05) 0%,
    rgba(0, 200, 150, 0.05) 100%
  );
}

.dark {
  /* Dark theme with modern green tones */
  --background: oklch(0.08 0.02 160);
  --foreground: oklch(0.95 0.02 160);
  --card: oklch(0.12 0.03 160);
  --card-foreground: oklch(0.95 0.02 160);
  --popover: oklch(0.08 0.02 160);
  --popover-foreground: oklch(0.95 0.02 160);
  --primary: oklch(0.8 0.2 160); /* Brighter vibrant green for dark mode */
  --primary-foreground: oklch(0.08 0.02 160);
  --secondary: oklch(0.77 0.18 170); /* Brighter teal green for dark mode */
  --secondary-foreground: oklch(0.08 0.02 160);
  --muted: oklch(0.18 0.03 160);
  --muted-foreground: oklch(0.7 0.05 160);
  --accent: oklch(0.75 0.17 155); /* Brighter emerald for dark mode */
  --accent-foreground: oklch(0.08 0.02 160);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.25 0.05 160);
  --input: oklch(0.2 0.04 160);
  --ring: oklch(0.8 0.2 160);
  --chart-1: oklch(0.8 0.2 160); /* Vibrant green */
  --chart-2: oklch(0.77 0.18 170); /* Teal green */
  --chart-3: oklch(0.75 0.17 155); /* Emerald */
  --chart-4: oklch(0.73 0.16 165); /* Medium green */
  --chart-5: oklch(0.7 0.14 150); /* Forest green */
  --sidebar: oklch(0.12 0.03 160);
  --sidebar-foreground: oklch(0.95 0.02 160);
  --sidebar-primary: oklch(0.8 0.2 160);
  --sidebar-primary-foreground: oklch(0.08 0.02 160);
  --sidebar-accent: oklch(0.75 0.17 155);
  --sidebar-accent-foreground: oklch(0.08 0.02 160);
  --sidebar-border: oklch(0.25 0.05 160);
  --sidebar-ring: oklch(0.8 0.2 160);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Dark theme green gradients */
  --gradient-primary: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(52, 211, 153, 0.15) 0%,
    rgba(16, 185, 129, 0.15) 100%
  );
  --gradient-accent: linear-gradient(135deg, #059669 0%, #047857 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(52, 211, 153, 0.08) 0%,
    rgba(16, 185, 129, 0.08) 100%
  );
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Modern gradient utility classes */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-primary-soft {
  background: var(--gradient-primary-soft);
}

.gradient-accent {
  background: var(--gradient-accent);
}

.gradient-card {
  background: var(--gradient-card);
}

/* Modern button styles with gradients */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 208, 132, 0.3);
}

/* Modern card with subtle gradient */
.card-gradient {
  background: var(--gradient-card);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Accent elements */
.accent-dot {
  background: var(--accent);
  border-radius: 50%;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
