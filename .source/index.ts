// @ts-nocheck -- skip type checking
import * as docs_3 from "../content/docs/quickstart.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_2 from "../content/docs/quickstart.mdx?collection=docs&hash=1752567730927"
import * as docs_1 from "../content/docs/index.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_0 from "../content/docs/index.mdx?collection=docs&hash=1752567730927"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/index.mdx"}, data: docs_0 }, { info: {"path":"index.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/index.zh.mdx"}, data: docs_1 }, { info: {"path":"quickstart.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quickstart.mdx"}, data: docs_2 }, { info: {"path":"quickstart.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quickstart.zh.mdx"}, data: docs_3 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.json"},"data":{"pages":["quickstart"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.zh.json"},"data":{"pages":["quickstart"]}}])