<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .text { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; font-weight: 600; }
    </style>
    <linearGradient id="xcodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5DB7F5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#147CE5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Xcode hammer icon -->
  <rect x="8" y="12" width="32" height="36" rx="6" fill="url(#xcodeGradient)"/>
  <path d="M16 20l16 8-16 8v-4l8-4-8-4v-4z" fill="white"/>
  <rect x="20" y="36" width="8" height="2" fill="white"/>
  
  <!-- Xcode text -->
  <text x="50" y="35" class="text" font-size="18" fill="#147CE5">Xcode</text>
</svg>